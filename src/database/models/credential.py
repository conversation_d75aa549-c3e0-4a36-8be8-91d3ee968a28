"""
Credential data models and enums.
"""
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional
import json


class CredentialType(Enum):
    """Credential types."""
    RPA = "rpa"
    VC_API = "vc_api"
    ADS = "ads"


class CredentialStatus(Enum):
    """Credential status."""
    AUTH = "auth"
    UNAUTH = "unauth"


class Platform(Enum):
    """Supported platforms."""
    AMAZON = "amazon"
    SHOPIFY = "shopify"
    ALIBABA_1688 = "1688"


@dataclass
class Credential:
    """
    Credential data model.
    """
    id: Optional[int] = None
    type: Optional[str] = None
    store_id: Optional[str] = None
    company_id: Optional[str] = None
    params: Optional[Dict[str, Any]] = None
    status: str = CredentialStatus.UNAUTH.value
    platform: Optional[str] = None
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)

    @classmethod
    def from_db_row(cls, row: tuple) -> 'Credential':
        """
        Create Credential from database row.
        
        Args:
            row: Database row tuple
            
        Returns:
            Credential instance
        """
        if not row:
            return None
            
        params = row[4]
        if params and isinstance(params, str):
            try:
                params = json.loads(params)
            except (json.JSONDecodeError, TypeError):
                params = None
        elif not isinstance(params, dict):
            params = None

        return cls(
            id=row[0],
            type=row[1],
            store_id=row[2],
            company_id=row[3],
            params=params,
            status=row[5],
            platform=row[6],
            create_time=row[7],
            update_time=row[8]
        )

    def serialize_params(self) -> Optional[str]:
        """Serialize params to JSON string for database storage."""
        return json.dumps(self.params) if self.params else None

    def validate(self) -> bool:
        """
        Validate credential data.
        
        Returns:
            True if valid, False otherwise
        """
        if not self.type or not self.store_id or not self.company_id:
            return False
            
        # Validate enum values
        try:
            if self.type and self.type not in [t.value for t in CredentialType]:
                return False
            if self.status and self.status not in [s.value for s in CredentialStatus]:
                return False
            if self.platform and self.platform not in [p.value for p in Platform]:
                return False
        except (AttributeError, ValueError):
            return False
            
        return True


@dataclass
class CredentialCreateRequest:
    """Request model for creating credentials."""
    type: str
    store_id: str
    company_id: str
    params: Dict[str, Any]
    status: str = CredentialStatus.UNAUTH.value
    platform: Optional[str] = None

    def to_credential(self) -> Credential:
        """Convert to Credential model."""
        return Credential(
            type=self.type,
            store_id=self.store_id,
            company_id=self.company_id,
            params=self.params,
            status=self.status,
            platform=self.platform
        )


@dataclass
class CredentialUpdateRequest:
    """Request model for updating credentials."""
    type: Optional[str] = None
    store_id: Optional[str] = None
    company_id: Optional[str] = None
    params: Optional[Dict[str, Any]] = None
    status: Optional[str] = None
    platform: Optional[str] = None

    def has_updates(self) -> bool:
        """Check if there are any fields to update."""
        return any([
            self.type is not None,
            self.store_id is not None,
            self.company_id is not None,
            self.params is not None,
            self.status is not None,
            self.platform is not None
        ])
