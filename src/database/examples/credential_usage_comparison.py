"""
Comparison of original vs optimized credential usage.
"""
from typing import Dict, Any
from ..tables.credential_back import CredentialTable as OriginalCredentialTable
from ..tables.credential import CredentialTable as OptimizedCredentialTable
from ..models.credential import (
    CredentialCreateRequest,
    CredentialUpdateRequest,
    CredentialType,
    CredentialStatus,
    Platform
)


def original_usage_example():
    """Example using the original CredentialTable."""
    table = OriginalCredentialTable()
    
    # 创建凭证 - 需要传递多个参数
    credential_id = table.create(
        type="rpa",
        store_id="store_123",
        company_id="company_456",
        params={"username": "test", "password": "secret"},
        status="unauth",
        platform="amazon"
    )
    
    # 获取凭证 - 返回字典，需要手动处理类型
    credential_dict = table.get(credential_id)
    if credential_dict:
        # 需要手动访问字典键，容易出错
        print(f"Type: {credential_dict['type']}")
        print(f"Status: {credential_dict['status']}")
        # JSON 参数需要手动处理
        params = credential_dict.get('params', {})
    
    # 更新凭证 - 需要传递多个可选参数
    success = table.update(
        credential_id=credential_id,
        status="auth",
        params={"username": "updated", "token": "new_token"}
    )
    
    return credential_dict


def optimized_usage_example():
    """Example using the optimized CredentialTable."""
    table = OptimizedCredentialTable()
    
    # 创建凭证 - 使用结构化请求对象
    create_request = CredentialCreateRequest(
        type=CredentialType.RPA.value,
        store_id="store_123",
        company_id="company_456",
        params={"username": "test", "password": "secret"},
        status=CredentialStatus.UNAUTH.value,
        platform=Platform.AMAZON.value
    )
    
    credential_id = table.create(create_request)
    
    # 获取凭证 - 返回强类型对象
    credential = table.get(credential_id)
    if credential:
        # 类型安全的属性访问
        print(f"Type: {credential.type}")
        print(f"Status: {credential.status}")
        # 自动处理的 JSON 参数
        params = credential.params or {}
        
        # 可以使用对象方法
        credential_dict = credential.to_dict()
        is_valid = credential.validate()
    
    # 更新凭证 - 使用结构化请求对象
    update_request = CredentialUpdateRequest(
        status=CredentialStatus.AUTH.value,
        params={"username": "updated", "token": "new_token"}
    )
    
    success = table.update(credential_id, update_request)
    
    # 使用枚举进行查询
    rpa_credentials = table.get_by_type(CredentialType.RPA)
    unauth_credentials = table.get_by_status(CredentialStatus.UNAUTH)
    
    return credential


def comparison_analysis():
    """
    对比分析两种方法的优缺点。
    """
    print("=== 原始方法 vs 优化方法对比 ===\n")
    
    print("1. 类型安全性:")
    print("   原始方法: 返回字典，运行时才能发现类型错误")
    print("   优化方法: 强类型对象，编译时/IDE 可以检查类型\n")
    
    print("2. 代码重复:")
    print("   原始方法: 多处重复的字典构建和 JSON 处理逻辑")
    print("   优化方法: 统一的数据模型，逻辑集中处理\n")
    
    print("3. 参数传递:")
    print("   原始方法: 方法参数过多，容易传错")
    print("   优化方法: 使用请求对象，参数结构化\n")
    
    print("4. 数据验证:")
    print("   原始方法: 缺乏统一的数据验证机制")
    print("   优化方法: 内置验证方法，确保数据一致性\n")
    
    print("5. 枚举使用:")
    print("   原始方法: 硬编码字符串，容易出错")
    print("   优化方法: 使用枚举，提供类型安全和自动补全\n")
    
    print("6. 可维护性:")
    print("   原始方法: 修改字段需要多处同步更新")
    print("   优化方法: 修改数据模型，自动同步所有使用处\n")
    
    print("7. 测试友好性:")
    print("   原始方法: 需要构造复杂的字典进行测试")
    print("   优化方法: 可以使用工厂方法创建测试数据\n")


if __name__ == "__main__":
    comparison_analysis()
    
    print("=== 原始方法示例 ===")
    original_result = original_usage_example()
    print(f"原始方法结果类型: {type(original_result)}")
    
    print("\n=== 优化方法示例 ===")
    optimized_result = optimized_usage_example()
    print(f"优化方法结果类型: {type(optimized_result)}")
