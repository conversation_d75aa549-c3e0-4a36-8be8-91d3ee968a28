"""
Optimized CredentialTable class for managing credential data.
"""
from typing import List, Optional
import psycopg
from ..base import BaseTable
from ..models.credential import (
    Credential, 
    CredentialCreateRequest, 
    CredentialUpdateRequest,
    CredentialType,
    CredentialStatus,
    Platform
)


class CredentialTable(BaseTable):
    """
    Optimized class for interacting with the credential table.
    """

    def create(self, request: CredentialCreateRequest) -> Optional[int]:
        """
        Create a new credential.

        Args:
            request: CredentialCreateRequest object

        Returns:
            ID of the created credential, or None if failed
        """
        credential = request.to_credential()
        
        if not credential.validate():
            return None

        with psycopg.connect(self.db_url) as conn:
            try:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        INSERT INTO agent.credential (type, store_id, company_id, params, status, platform)
                        VALUES (%s, %s, %s, %s, %s, %s)
                        RETURNING id
                        """,
                        (
                            credential.type,
                            credential.store_id,
                            credential.company_id,
                            credential.serialize_params(),
                            credential.status,
                            credential.platform
                        )
                    )
                    credential_id = cur.fetchone()[0]
                    conn.commit()
                    return credential_id
            except Exception:
                conn.rollback()
                return None

    def get(self, credential_id: int) -> Optional[Credential]:
        """
        Get a credential by ID.

        Args:
            credential_id: ID of the credential to retrieve

        Returns:
            Credential object, or None if not found
        """
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT id, type, store_id, company_id, params, status, platform, 
                           create_time, update_time
                    FROM agent.credential
                    WHERE id = %s
                    """,
                    (credential_id,)
                )
                result = cur.fetchone()
                return Credential.from_db_row(result) if result else None

    def update(self, credential_id: int, request: CredentialUpdateRequest) -> bool:
        """
        Update an existing credential.

        Args:
            credential_id: ID of the credential to update
            request: CredentialUpdateRequest object

        Returns:
            True if the update was successful, False otherwise
        """
        if not credential_id or not request.has_updates():
            return False

        with psycopg.connect(self.db_url) as conn:
            try:
                with conn.cursor() as cur:
                    params_json = None
                    if request.params is not None:
                        import json
                        params_json = json.dumps(request.params)

                    cur.execute(
                        """
                        UPDATE agent.credential 
                        SET 
                            type = COALESCE(%s, type),
                            store_id = COALESCE(%s, store_id),
                            company_id = COALESCE(%s, company_id),
                            params = COALESCE(%s, params),
                            status = COALESCE(%s, status),
                            platform = COALESCE(%s, platform),
                            update_time = CURRENT_TIMESTAMP
                        WHERE id = %s
                        """,
                        (
                            request.type,
                            request.store_id,
                            request.company_id,
                            params_json,
                            request.status,
                            request.platform,
                            credential_id
                        )
                    )
                    conn.commit()
                    return cur.rowcount > 0
            except Exception:
                conn.rollback()
                return False

    def update_by_store_type_platform(
        self,
        store_id: str,
        type: str,
        platform: str,
        request: CredentialUpdateRequest
    ) -> bool:
        """
        Update a credential by store_id, type, and platform lookup.

        Args:
            store_id: ID of the store
            type: credential type
            platform: platform name
            request: CredentialUpdateRequest object

        Returns:
            True if the update was successful, False otherwise
        """
        if not request.has_updates():
            return False

        with psycopg.connect(self.db_url) as conn:
            try:
                with conn.cursor() as cur:
                    params_json = None
                    if request.params is not None:
                        import json
                        params_json = json.dumps(request.params)

                    cur.execute(
                        """
                        UPDATE agent.credential 
                        SET 
                            company_id = COALESCE(%s, company_id),
                            params = COALESCE(%s, params),
                            status = COALESCE(%s, status),
                            update_time = CURRENT_TIMESTAMP
                        WHERE store_id = %s AND type = %s AND platform = %s
                        """,
                        (
                            request.company_id,
                            params_json,
                            request.status,
                            store_id,
                            type,
                            platform
                        )
                    )
                    conn.commit()
                    return cur.rowcount > 0
            except Exception:
                conn.rollback()
                return False

    def get_by_store(self, store_id: str) -> List[Credential]:
        """
        Get all credentials for a specific store.

        Args:
            store_id: ID of the store

        Returns:
            List of Credential objects
        """
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT id, type, store_id, company_id, params, status, platform, 
                           create_time, update_time
                    FROM agent.credential
                    WHERE store_id = %s
                    ORDER BY create_time DESC
                    """,
                    (store_id,)
                )
                results = cur.fetchall()
                return [Credential.from_db_row(result) for result in results]

    def get_by_company_id(
        self, 
        company_id: str, 
        limit: int = 100, 
        offset: int = 0
    ) -> List[Credential]:
        """
        Get all credentials for a specific company.

        Args:
            company_id: ID of the company
            limit: Maximum number of records to return
            offset: Number of records to skip

        Returns:
            List of Credential objects
        """
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT id, type, store_id, company_id, params, status, platform, 
                           create_time, update_time
                    FROM agent.credential
                    WHERE company_id = %s
                    ORDER BY create_time DESC
                    LIMIT %s OFFSET %s
                    """,
                    (company_id, limit, offset)
                )
                results = cur.fetchall()
                return [Credential.from_db_row(result) for result in results]

    def get_by_store_type_platform(
        self, 
        store_id: str, 
        type: str, 
        platform: str
    ) -> Optional[Credential]:
        """
        Get a credential by store_id, type, and platform.

        Args:
            store_id: ID of the store
            type: credential type
            platform: platform name

        Returns:
            Credential object, or None if not found
        """
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT id, type, store_id, company_id, params, status, platform, 
                           create_time, update_time
                    FROM agent.credential
                    WHERE store_id = %s AND type = %s AND platform = %s
                    ORDER BY create_time DESC
                    LIMIT 1
                    """,
                    (store_id, type, platform)
                )
                result = cur.fetchone()
                return Credential.from_db_row(result) if result else None

    def delete(self, credential_id: int) -> bool:
        """
        Delete a credential.

        Args:
            credential_id: ID of the credential to delete

        Returns:
            True if the deletion was successful, False otherwise
        """
        with psycopg.connect(self.db_url) as conn:
            try:
                with conn.cursor() as cur:
                    cur.execute(
                        "DELETE FROM agent.credential WHERE id = %s",
                        (credential_id,)
                    )
                    conn.commit()
                    return cur.rowcount > 0
            except Exception:
                conn.rollback()
                return False

    # 便捷方法，使用枚举
    def get_by_type(self, credential_type: CredentialType) -> List[Credential]:
        """Get credentials by type using enum."""
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT id, type, store_id, company_id, params, status, platform, 
                           create_time, update_time
                    FROM agent.credential
                    WHERE type = %s
                    ORDER BY create_time DESC
                    """,
                    (credential_type.value,)
                )
                results = cur.fetchall()
                return [Credential.from_db_row(result) for result in results]

    def get_by_status(self, status: CredentialStatus) -> List[Credential]:
        """Get credentials by status using enum."""
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT id, type, store_id, company_id, params, status, platform, 
                           create_time, update_time
                    FROM agent.credential
                    WHERE status = %s
                    ORDER BY create_time DESC
                    """,
                    (status.value,)
                )
                results = cur.fetchall()
                return [Credential.from_db_row(result) for result in results]
